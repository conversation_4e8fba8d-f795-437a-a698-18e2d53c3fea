#!/usr/bin/env python3
"""
TNGD Unified Backup System
==========================

A unified Python-based backup solution that eliminates .bat file dependencies.
Supports CLI usage with flexible date input options for backing up Devo tables to OSS.

Features:
- No arguments: Back up today's data
- Single date: Back up specific date
- Date range: Back up data for each date in range
- Modular design with reusable functions
- Robust logging and exception handling
- Progress updates and cleanup

Usage:
    python tngd_backup.py                           # Today's data
    python tngd_backup.py 01 March 2025             # Single date
    python tngd_backup.py --start "01 March 2025" --end "31 March 2025"  # Date range

Author: TNGD Backup System
Version: 1.0.0
"""

import argparse
import json
import logging
import os
import sys
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import existing core modules
try:
    from core.devo_client import DevoClient
    from core.storage_manager import StorageManager
    from core.config_manager import ConfigManager
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please ensure all required modules are available in the core/ and utils/ directories.")
    sys.exit(1)


class TngdBackupCLI:
    """
    Unified TNGD Backup CLI class that handles all backup operations.
    """
    
    def __init__(self):
        """Initialize the backup CLI with configuration and clients."""
        self.config_manager = ConfigManager()
        self.config = self.config_manager.config

        # Initialize basic logger immediately
        self.logger = logging.getLogger(__name__)

        self.devo_client = None
        self.storage_manager = None
        self.temp_files = []  # Track temporary files for cleanup
        
    def setup_logging(self, verbose: bool = False) -> None:
        """
        Setup logging configuration.

        Args:
            verbose: Enable verbose logging
        """
        log_level = logging.DEBUG if verbose else logging.INFO

        # Setup basic logging configuration
        log_dir = self.config_manager.get('logging', 'log_dir', 'logs')
        os.makedirs(log_dir, exist_ok=True)

        log_file = os.path.join(log_dir, 'tngd_backup.log')

        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )

        self.logger = logging.getLogger(__name__)
        
    def initialize_clients(self) -> None:
        """Initialize Devo and Storage clients."""
        try:
            self.logger.info("Initializing Devo API client...")
            self.devo_client = DevoClient()
            
            self.logger.info("Initializing Storage Manager...")
            self.storage_manager = StorageManager(self.config_manager)
            
            self.logger.info("All clients initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize clients: {str(e)}")
            raise
            
    def load_table_list(self, config_path: Optional[str] = None) -> List[str]:
        """
        Load list of tables from configuration file.
        
        Args:
            config_path: Optional path to table configuration file
            
        Returns:
            List of table names
            
        Raises:
            FileNotFoundError: If configuration file not found
            json.JSONDecodeError: If configuration file is invalid JSON
        """
        # Determine configuration file path
        if config_path:
            table_config_path = Path(config_path)
        else:
            # Use default paths - try common locations
            default_paths = [
                'tabletest/tables.json',
                'config/tables.json',
                'backup/tables.json'
            ]

            table_config_path = None
            for path in default_paths:
                if Path(path).exists():
                    table_config_path = Path(path)
                    break

            if not table_config_path:
                # Use fallback tables
                fallback_tables = [
                    "my.app.tngd.waf",
                    "my.app.tngd.actiontraillinux"
                ]
                self.logger.warning("Using fallback table list - no configuration file found")
                return fallback_tables
        
        if not table_config_path.exists():
            raise FileNotFoundError(f"Table configuration file not found: {table_config_path}")
            
        try:
            with open(table_config_path, 'r', encoding='utf-8') as f:
                tables = json.load(f)
                
            if not isinstance(tables, list):
                raise ValueError("Table configuration must be a JSON array")
                
            self.logger.info(f"Loaded {len(tables)} tables from {table_config_path}")
            return tables
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in table configuration file: {e}")

    def parse_date_string(self, date_str: str) -> datetime:
        """
        Parse date string in various formats.

        Args:
            date_str: Date string to parse

        Returns:
            Parsed datetime object

        Raises:
            ValueError: If date string cannot be parsed
        """
        # Common date formats to try
        date_formats = [
            "%d %B %Y",      # 01 March 2025
            "%d %b %Y",      # 01 Mar 2025
            "%Y-%m-%d",      # 2025-03-01
            "%m/%d/%Y",      # 03/01/2025
            "%d/%m/%Y",      # 01/03/2025
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        raise ValueError(f"Unable to parse date string: {date_str}")

    def generate_date_range(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """
        Generate list of dates between start and end dates (inclusive).

        Args:
            start_date: Start date
            end_date: End date

        Returns:
            List of datetime objects for each date in range

        Raises:
            ValueError: If start_date is after end_date
        """
        if start_date > end_date:
            raise ValueError("Start date cannot be after end date")

        dates = []
        current_date = start_date

        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)

        self.logger.info(f"Generated date range: {len(dates)} dates from {start_date.date()} to {end_date.date()}")
        return dates

    def backup_table(self, table_name: str, target_date: datetime) -> Dict[str, Any]:
        """
        Execute the full backup routine for a given table and date.

        Args:
            table_name: Name of the table to backup
            target_date: Date to backup data for

        Returns:
            Dictionary containing backup results and metadata
        """
        backup_start_time = datetime.now()
        result = {
            'table_name': table_name,
            'target_date': target_date.date(),
            'status': 'started',
            'start_time': backup_start_time,
            'rows_processed': 0,
            'file_size_mb': 0,
            'upload_path': None,
            'error': None
        }

        try:
            self.logger.info(f"Starting backup for table {table_name} on {target_date.date()}")

            # Step 1: Query Devo API for table data
            self.logger.info(f"Querying Devo API for {table_name}...")

            # Build query for the specific date
            date_str = target_date.strftime("%Y-%m-%d")
            query = f"from {table_name} where eventdate >= '{date_str}' and eventdate < '{(target_date + timedelta(days=1)).strftime('%Y-%m-%d')}'"

            # Execute query with appropriate timeout
            timeout = self.config_manager.get('backup', 'default_timeout', 1800)
            if self.devo_client:
                query_results = self.devo_client.execute_query(
                    query=query,
                    timeout=timeout,
                    table_name=table_name
                )
            else:
                raise RuntimeError("Devo client not initialized")

            result['rows_processed'] = len(query_results)
            self.logger.info(f"Retrieved {result['rows_processed']} rows for {table_name}")

            if result['rows_processed'] == 0:
                self.logger.warning(f"No data found for {table_name} on {target_date.date()}")
                result['status'] = 'no_data'
                return result

            # Step 2: Save results to temporary local file
            temp_file = self._create_temp_file(table_name, target_date)
            self.temp_files.append(temp_file)

            self.logger.info(f"Saving data to temporary file: {temp_file}")
            self._save_query_results(query_results, temp_file)

            # Get file size
            file_size = os.path.getsize(temp_file)
            result['file_size_mb'] = file_size / (1024 * 1024)

            # Step 3: Upload to OSS
            upload_path = self._generate_oss_path(table_name, target_date)
            result['upload_path'] = upload_path

            self.logger.info(f"Uploading {table_name}_{target_date.date().strftime('%Y-%m-%d')}.json.gz to OSS...")
            upload_success, upload_details = self.upload_to_oss(temp_file, upload_path)

            if not upload_success:
                raise RuntimeError(f"Upload failed: {upload_details}")

            # Step 4: Cleanup temporary file
            self.cleanup_temp(temp_file)

            result['status'] = 'completed'
            result['end_time'] = datetime.now()
            result['duration_seconds'] = (result['end_time'] - backup_start_time).total_seconds()

            self.logger.info(f"Successfully backed up {table_name} for {target_date.date()}")
            return result

        except Exception as e:
            result['status'] = 'failed'
            result['error'] = str(e)
            result['end_time'] = datetime.now()
            result['duration_seconds'] = (result['end_time'] - backup_start_time).total_seconds()

            self.logger.error(f"Backup failed for {table_name} on {target_date.date()}: {str(e)}")

            # Cleanup on failure - check if temp_file was created
            temp_file_path = self._create_temp_file(table_name, target_date)
            if temp_file_path in self.temp_files:
                self.cleanup_temp(temp_file_path)

            return result

    def _create_temp_file(self, table_name: str, target_date: datetime) -> str:
        """
        Create a temporary file for storing query results.

        Args:
            table_name: Name of the table
            target_date: Target date for backup

        Returns:
            Path to temporary file
        """
        temp_dir = self.config_manager.get('storage', 'temp_dir', 'temp')
        os.makedirs(temp_dir, exist_ok=True)

        date_str = target_date.strftime('%Y-%m-%d')
        temp_filename = f"{table_name}_{date_str}.json"
        temp_path = os.path.join(temp_dir, temp_filename)

        return temp_path

    def _save_query_results(self, results: List[Dict[str, Any]], file_path: str) -> None:
        """
        Save query results to a JSON file.

        Args:
            results: Query results to save
            file_path: Path to save file
        """
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

    def _generate_oss_path(self, table_name: str, target_date: datetime) -> str:
        """
        Generate OSS storage path for the backup file.

        Args:
            table_name: Name of the table
            target_date: Target date for backup

        Returns:
            OSS storage path
        """
        # Get path template from configuration
        path_template = self.config.get('storage', {}).get('oss_path_template',
                                                          'Devo/{month_name_str}/week {week_number}/{date_str}/{table_name}_{date_str}.tar.gz')

        # Calculate date components
        month_name_str = target_date.strftime('%B')  # Full month name
        week_number = (target_date.day - 1) // 7 + 1  # Week number in month
        date_str = target_date.strftime('%Y-%m-%d')

        # Format the path
        oss_path = path_template.format(
            month_name_str=month_name_str,
            week_number=week_number,
            date_str=date_str,
            table_name=table_name
        )

        return oss_path

    def upload_to_oss(self, local_file: str, oss_path: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload file to OSS storage.

        Args:
            local_file: Path to local file to upload
            oss_path: OSS destination path

        Returns:
            Tuple of (success_flag, upload_details)
        """
        try:
            # Create a temporary directory with the file for compression
            temp_upload_dir = tempfile.mkdtemp(prefix='tngd_upload_')
            temp_file_in_dir = os.path.join(temp_upload_dir, os.path.basename(local_file))

            # Copy the file to the temporary directory
            import shutil
            shutil.copy2(local_file, temp_file_in_dir)

            try:
                # Use the storage manager's compress_and_upload method
                if self.storage_manager:
                    success, operation_details = self.storage_manager.compress_and_upload(
                        temp_upload_dir, oss_path, verify_integrity=True
                    )

                    if success:
                        return True, {
                            'oss_path': oss_path,
                            'operation_details': operation_details
                        }
                    else:
                        return False, {'error': 'Compression or upload failed'}
                else:
                    return False, {'error': 'Storage manager not initialized'}

            finally:
                # Clean up temporary directory
                shutil.rmtree(temp_upload_dir, ignore_errors=True)

        except Exception as e:
            self.logger.error(f"Upload failed: {str(e)}")
            return False, {'error': str(e)}

    def cleanup_temp(self, file_path: str) -> None:
        """
        Safely remove temporary files post-upload.

        Args:
            file_path: Path to temporary file to remove
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                self.logger.debug(f"Cleaned up temporary file: {file_path}")

                # Remove from tracking list
                if file_path in self.temp_files:
                    self.temp_files.remove(file_path)

        except Exception as e:
            self.logger.warning(f"Failed to cleanup temporary file {file_path}: {str(e)}")

    def cleanup_all_temp_files(self) -> None:
        """Clean up all tracked temporary files."""
        for temp_file in self.temp_files.copy():
            self.cleanup_temp(temp_file)


def parse_args() -> argparse.Namespace:
    """
    Parse and validate CLI arguments.
    
    Returns:
        Parsed arguments namespace
    """
    parser = argparse.ArgumentParser(
        description="TNGD Unified Backup System - Backup Devo tables to OSS",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    # Back up today's data
  %(prog)s 01 March 2025                     # Back up specific date
  %(prog)s --start "01 March 2025" --end "31 March 2025"  # Back up date range
  %(prog)s --dry-run                         # Validate configuration only
  %(prog)s --tables my.app.tngd.waf          # Back up specific table only
        """
    )
    
    # Single date (positional arguments)
    parser.add_argument(
        'date_parts',
        nargs='*',
        help='Date parts for single date backup (e.g., "01 March 2025")'
    )

    # Date range arguments (both required together)
    parser.add_argument(
        '--start',
        type=str,
        help='Start date for range backup (e.g., "01 March 2025")'
    )

    parser.add_argument(
        '--end',
        type=str,
        help='End date for range backup (e.g., "31 March 2025")'
    )
    
    # Configuration options
    parser.add_argument(
        '--config', 
        type=str, 
        help='Path to table configuration file (default: auto-detect)'
    )
    
    parser.add_argument(
        '--tables', 
        nargs='+', 
        help='Specific table names to backup (overrides config file)'
    )
    
    # Operation modes
    parser.add_argument(
        '--dry-run', 
        action='store_true', 
        help='Validate configuration and connectivity only, no backup'
    )
    
    parser.add_argument(
        '--verbose', 
        action='store_true', 
        help='Enable verbose logging'
    )
    
    # Processing options
    parser.add_argument(
        '--chunk-size', 
        type=int, 
        help='Override default chunk size for data processing'
    )
    
    parser.add_argument(
        '--timeout', 
        type=int, 
        help='Override default timeout in seconds'
    )
    
    parser.add_argument(
        '--max-retries', 
        type=int, 
        help='Override default maximum retry attempts'
    )
    
    args = parser.parse_args()

    # Validate that start and end are used together
    if (args.start and not args.end) or (args.end and not args.start):
        parser.error("--start and --end must be used together for date range backup")

    # Validate that date_parts and date range are not used together
    if args.date_parts and (args.start or args.end):
        parser.error("Cannot use both positional date arguments and --start/--end range arguments")

    return args


def main():
    """Main entry point for the TNGD Backup CLI."""
    backup_cli = None

    try:
        # Parse command line arguments
        args = parse_args()

        # Initialize backup CLI
        backup_cli = TngdBackupCLI()
        backup_cli.setup_logging(verbose=args.verbose)

        backup_cli.logger.info("=== TNGD Unified Backup System Started ===")
        backup_cli.logger.info(f"Arguments: {vars(args)}")

        # Handle dry run mode
        if args.dry_run:
            backup_cli.logger.info("DRY RUN MODE: Validating configuration only")

            # Test configuration loading
            if args.tables:
                tables = args.tables
                backup_cli.logger.info(f"Would use specified tables: {tables}")
            else:
                tables = backup_cli.load_table_list(args.config)
                backup_cli.logger.info(f"Loaded {len(tables)} tables from configuration")

            # Test client initialization
            backup_cli.initialize_clients()
            backup_cli.logger.info("All clients initialized successfully")

            # Test date parsing
            dates = determine_target_dates(args, backup_cli)
            backup_cli.logger.info(f"Would process {len(dates)} dates: {[d.date() for d in dates]}")

            backup_cli.logger.info("=== DRY RUN COMPLETED SUCCESSFULLY ===")
            return

        # Initialize clients
        backup_cli.initialize_clients()

        # Load table configuration
        if args.tables:
            tables = args.tables
            backup_cli.logger.info(f"Using specified tables: {tables}")
        else:
            tables = backup_cli.load_table_list(args.config)

        backup_cli.logger.info(f"Backup will process {len(tables)} tables")

        # Determine target dates
        dates = determine_target_dates(args, backup_cli)
        backup_cli.logger.info(f"Processing {len(dates)} dates")

        # Execute backup for each date and table
        total_operations = len(dates) * len(tables)
        completed_operations = 0
        failed_operations = 0

        backup_cli.logger.info(f"Starting backup process: {total_operations} total operations")
        print(f"\n🚀 Starting backup process: {total_operations} total operations")
        print(f"📅 Processing {len(dates)} dates with {len(tables)} tables each")
        print("=" * 60)

        for date_idx, target_date in enumerate(dates, 1):
            backup_cli.logger.info(f"Processing date: {target_date.date()}")
            print(f"\n📅 Processing date {date_idx}/{len(dates)}: {target_date.date()}")

            for table_idx, table_name in enumerate(tables, 1):
                # Progress indicator
                current_op = (date_idx - 1) * len(tables) + table_idx
                progress_percent = (current_op / total_operations) * 100

                print(f"  📊 [{current_op:3d}/{total_operations}] ({progress_percent:5.1f}%) Processing {table_name}...", end=" ")

                result = backup_cli.backup_table(table_name, target_date)

                if result['status'] == 'completed':
                    completed_operations += 1
                    file_size_mb = result.get('file_size_mb', 0)
                    duration = result.get('duration_seconds', 0)
                    print(f"✅ Success ({file_size_mb:.1f}MB, {duration:.1f}s)")
                    backup_cli.logger.info(f"✅ {table_name}_{target_date.date().strftime('%Y-%m-%d')}: Success ({file_size_mb:.1f}MB, {duration:.1f}s)")
                elif result['status'] == 'no_data':
                    completed_operations += 1
                    print(f"⚠️  No data")
                    backup_cli.logger.info(f"⚠️  {table_name}_{target_date.date().strftime('%Y-%m-%d')}: No data found")
                else:
                    failed_operations += 1
                    error_msg = result.get('error', 'Unknown error')
                    print(f"❌ Failed: {error_msg}")
                    backup_cli.logger.error(f"❌ {table_name}_{target_date.date().strftime('%Y-%m-%d')}: {error_msg}")

            # Date completion summary
            date_completed = sum(1 for i in range((date_idx-1)*len(tables), date_idx*len(tables)) if i < completed_operations + failed_operations)
            date_success = sum(1 for i in range((date_idx-1)*len(tables), min(date_idx*len(tables), completed_operations + failed_operations)) if i < completed_operations)
            print(f"  📋 Date {target_date.date()} completed: {date_success}/{len(tables)} successful")

        # Final summary
        backup_cli.logger.info("=== BACKUP PROCESS COMPLETED ===")
        backup_cli.logger.info(f"Total operations: {total_operations}")
        backup_cli.logger.info(f"Completed successfully: {completed_operations}")
        backup_cli.logger.info(f"Failed: {failed_operations}")

        # Enhanced summary with statistics
        success_rate = (completed_operations / total_operations * 100) if total_operations > 0 else 0

        print(f"\n" + "=" * 60)
        print(f"🎯 BACKUP SUMMARY")
        print(f"=" * 60)
        print(f"📊 Total operations:      {total_operations}")
        print(f"✅ Completed successfully: {completed_operations}")
        print(f"❌ Failed:                {failed_operations}")
        print(f"📈 Success rate:          {success_rate:.1f}%")
        print(f"📅 Dates processed:       {len(dates)}")
        print(f"📋 Tables per date:       {len(tables)}")

        # Get log file path safely
        log_file_path = os.path.join(backup_cli.config_manager.get('logging', 'log_dir', 'logs'), 'tngd_backup.log')

        if failed_operations > 0:
            print(f"\n⚠️  {failed_operations} operations failed. Check logs for details.")
            print(f"📄 Log file: {log_file_path}")
            sys.exit(1)
        else:
            print(f"\n🎉 All backup operations completed successfully!")
            print(f"📄 Log file: {log_file_path}")

    except KeyboardInterrupt:
        print("\nBackup interrupted by user")
        if backup_cli:
            backup_cli.cleanup_all_temp_files()
        sys.exit(1)
    except Exception as e:
        print(f"Backup failed: {str(e)}")
        if backup_cli and backup_cli.logger:
            backup_cli.logger.error(f"Fatal error: {str(e)}")
            backup_cli.cleanup_all_temp_files()
        sys.exit(1)
    finally:
        # Ensure cleanup
        if backup_cli:
            backup_cli.cleanup_all_temp_files()


def determine_target_dates(args: argparse.Namespace, backup_cli: TngdBackupCLI) -> List[datetime]:
    """
    Determine target dates based on command line arguments.

    Args:
        args: Parsed command line arguments
        backup_cli: Backup CLI instance

    Returns:
        List of target dates
    """
    if args.start and args.end:
        # Date range mode
        start_date = backup_cli.parse_date_string(args.start)
        end_date = backup_cli.parse_date_string(args.end)
        return backup_cli.generate_date_range(start_date, end_date)

    elif args.date_parts:
        # Single date mode
        date_str = ' '.join(args.date_parts)
        target_date = backup_cli.parse_date_string(date_str)
        return [target_date]

    else:
        # Default: today's data (actually yesterday for daily backups)
        yesterday = datetime.now() - timedelta(days=1)
        return [yesterday]


if __name__ == "__main__":
    main()
